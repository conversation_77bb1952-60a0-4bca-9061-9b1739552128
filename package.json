{"name": "findit", "version": "1.0.0", "description": "FindIt job application platform - Full stack application", "private": true, "scripts": {"install:client": "cd client && npm install", "install:server": "cd server && npm install", "install:all": "npm run install:client && npm run install:server", "build:client": "cd client && npm run build", "build": "npm run build:client", "start:client": "cd client && npm start", "start:server": "cd server && npm start", "dev:client": "cd client && npm start", "dev:server": "cd server && npm run dev", "dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "vercel-build": "npm run install:all && npm run build:client"}, "keywords": ["job-application", "angular", "nodejs", "express", "mysql"], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}