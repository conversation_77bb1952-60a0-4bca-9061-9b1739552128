{"version": 2, "builds": [{"src": "client/package.json", "use": "@vercel/static-build", "config": {"distDir": "dist/client"}}, {"src": "api/index.js", "use": "@vercel/node", "config": {"maxDuration": 30}}], "routes": [{"src": "/api/(.*)", "dest": "/api/index.js"}, {"src": "/(.*)", "dest": "/client/dist/client/$1"}], "outputDirectory": "client/dist/client", "installCommand": "npm install --prefix client && npm install --prefix server", "buildCommand": "cd client && npm run build", "env": {"NODE_ENV": "production"}}