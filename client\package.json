{"name": "client", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production", "build:dev": "ng build --configuration development", "watch": "ng build --watch --configuration development", "test": "ng test", "vercel-build": "ng build --configuration production"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/common": "^16.2.12", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "@angular/material": "^16.2.12", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "bootstrap": "^5.3.6", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.10", "@angular/cli": "^16.2.10", "@angular/compiler-cli": "^16.2.0", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.1.3"}}