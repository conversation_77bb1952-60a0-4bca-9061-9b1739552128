# Server Configuration
PORT=3000
NODE_ENV=production

# Database Configuration (Use your production database)
DB_HOST=your-database-host.example.com
DB_USER=your_database_username
DB_PASSWORD=your_database_password_here
DB_NAME=findit_db
DB_PORT=3306

# JWT Configuration
JWT_SECRET=replace_with_your_secure_jwt_secret_key
JWT_EXPIRES_IN=1d

# Logging
LOG_LEVEL=info

# CORS Configuration (Optional - for specific frontend domains)
ALLOWED_ORIGINS=https://your-vercel-app.vercel.app
